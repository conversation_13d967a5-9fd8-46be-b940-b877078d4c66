<!--
  机构放款金额Top5组件
  功能：
  1. 展示机构放款金额的前5名数据
  2. 提供3D柱状图可视化展示
  3. 显示累计放款金额和笔数
  4. 支持图表交互和提示
-->
<template>
  <div class="AmountTop5-dataAcquisition">
    <!-- 头部标题区域 -->
    <div class="AmountTop5-headerImg">
      <div>机构放款动态</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <div class="AmountTop5-bottomClass">
      <!-- 顶部统计信息 -->
      <div class="AmountTop5-top-summary">
        <span>累计放款金额：</span>
        <span class="AmountTop5-highlight">{{ amount }}</span
        >亿元 <span class="AmountTop5-highlight2">{{ parseInt(count) }}</span
        >笔
      </div>
      <!-- 图表容器 -->
      <div ref="barChart" class="AmountTop5-echart-bar"></div>
    </div>
    <dialog-bar-park2
      :visible="dialogVisible"
      :data="parkList"
      title="机构放款动态"
      :display-count="12"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBarPark2 from "@/views/components/dialogBar-park2.vue";

export default {
  name: "AmountTop5",
  components: { DialogBarPark2 },
  data() {
    return {
      dialogVisible: false,
      // 银行名称列表
      banks: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
      ],
      values: [], // 放款金额数据（单位：亿）
      amount: 0, // 累计放款金额
      count: 0, // 累计放款笔数
      counts: [3222, 2888, 2540, 2100, 1800], // 放款笔数数据
      dialogAutoPlayTimer: null, // 自动播放定时器
      timerIndex: 0, // 当前播放的索引
      addResizeListener: false, // 是否添加了resize监听器
      parkList: [],
      top5List: [], // Top5数据列表
    };
  },
  mounted() {
    // 获取数据并初始化图表
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "累计放款笔数",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构放款金额",
      },
    ]).then((res) => {
      this.amount = res.data.data[0].indexValue;
      this.count = res.data.data[1].indexValue;
      this.top5List = JSON.parse(res.data.data[2].bizContent);
      this.parkList = this.top5List
        .map((item) => {
          return { ...item, value: item.loanAmt.replace("亿元", "") };
        })
        .sort((a, b) => b.value - a.value);
      // 处理数据：去除单位并转换为数字
      this.values = this.top5List.map((item) =>
        item.loanAmt.replace("亿元", "")
      );
      this.values = this.values.map((item) => Number(item));

      this.counts = this.top5List.map((item) => item.loanSum);
      this.banks = this.top5List.map((item) => item.shortName);
      this.initBarChart();
    });
  },
  methods: {
    // 初始化3D柱状图
    initBarChart() {
      const chart = echarts.init(this.$refs.barChart);
      const offsetX = 10; // X轴偏移量
      const offsetY = 5; // Y轴偏移量

      // 绘制左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - offsetX, shape.y - offsetY];
          const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });

      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
          const c4 = [shape.x + offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + offsetX, shape.y - offsetY]; //右点
          const c3 = [shape.x, shape.y - offsetX];
          const c4 = [shape.x - offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 注册三个面图形
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);

      this.updateEcharts(chart, 0, 5, this.values, this.counts, this.banks);
      if (this.addResizeListener) return;
      // 监听窗口大小变化，调整图表大小
      window.addEventListener("resize", () => chart.resize());
    },
    // 第三个参数控制最大显示量
    updateEcharts(chart, startIndex, length, oldValues, oldCounts, oldBanks) {
      const primaryColor = "42,205,253"; // 主色调
      const isMaxShow = true; // 是否显示最大值
      const VALUE = oldValues.slice(startIndex, length) || []; // 当前值数组
      const counts = oldCounts.slice(startIndex, length) || []; // 当前值数组
      const banks = oldBanks.slice(startIndex, length) || []; // 当前值数组
      // 图表配置项
      const option = {
        // 提示框配置
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            const item = params[1];
            const value = item.value;
            const count = 322; // 或 this.counts[item.dataIndex]
            return (
              '<div style="text-align:center;">' +
              `<span style="color:#ffce7c;font-size:16px;">${value}亿</span><br/>` +
              `<span style="color:#7cebff;font-size:16px;">${count}笔</span>` +
              "</div>"
            );
          },
          backgroundColor: "rgba(10,29,56,0.95)",
          borderColor: "#0a4d8f",
          borderWidth: 1,
          extraCssText: "box-shadow:0 0 8px #0a4d8f;",
        },
        // 图表网格配置
        grid: {
          left: "0%",
          right: "0%",
          top: "30%",
          bottom: "2%",
          containLabel: true,
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: banks,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            interval: 0,
            formatter: function (value) {
              return value.replace(/(.{4})/g, "$1\n");
            },
          },
        },
        // Y轴配置
        yAxis: {
          show: false,
        },
        // 系列配置
        series: [
          // 最大值系列
          isMaxShow
            ? {
                type: "custom",
                renderItem: function (params, api) {
                  const location = api.coord([api.value(0), api.value(1)]);
                  return {
                    type: "group",
                    children: [
                      {
                        type: "CubeLeft",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .1)`,
                        },
                      },
                      {
                        type: "CubeRight",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .3)`,
                        },
                      },
                      {
                        type: "CubeTop",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .4)`,
                        },
                      },
                    ],
                  };
                },
                data: VALUE,
              }
            : null,
          // 实际值系列
          {
            type: "custom",
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              return {
                type: "group",
                children: [
                  {
                    type: "CubeLeft",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: `rgba(${primaryColor}, .5)`,
                    },
                  },
                  {
                    type: "CubeRight",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: `rgba(${primaryColor},1)`,
                        },
                        {
                          offset: 1,
                          color: `rgba(${primaryColor},.5)`,
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeTop",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: `rgba(${primaryColor},1)`,
                        },
                        {
                          offset: 1,
                          color: `#419EFF`,
                        },
                      ]),
                    },
                  },
                ],
              };
            },
            data: VALUE,
          },
          // 标签系列
          {
            type: "bar",
            label: {
              show: true,
              position: "top",
              padding: [0, 0, this.$autoFontSize(5), 0],
              formatter: (params) => {
                const value = VALUE[params.dataIndex];
                const count = counts[params.dataIndex];
                return `{gold|${value}亿元}\n{blue|${count}}`;
              },
              rich: {
                gold: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [0, 0, this.$autoFontSize(5), 0],
                  align: "center",
                },
                blue: {
                  color: "#7cebff",
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
            itemStyle: {
              color: "transparent",
            },
            tooltip: {},
            data: VALUE,
          },
        ],
      };
      chart.setOption(option);
      if (oldValues && oldValues.length) {
        oldValues = JSON.parse(JSON.stringify(oldValues));
        oldValues.push(oldValues.shift());
      }
      if (oldCounts && oldCounts.length) {
        oldCounts = JSON.parse(JSON.stringify(oldCounts));
        oldCounts.push(oldCounts.shift());
      }
      if (oldBanks && oldBanks.length) {
        oldBanks = JSON.parse(JSON.stringify(oldBanks));
        oldBanks.push(oldBanks.shift());
      }
      if (this.dialogAutoPlayTimer) {
        clearTimeout(this.dialogAutoPlayTimer);
      }
      this.dialogAutoPlayTimer = setTimeout(() => {
        this.updateEcharts(
          chart,
          startIndex,
          length,
          oldValues,
          oldCounts,
          oldBanks
        );
      }, 3000);
    },
    handleClick() {
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
    },
  },
  beforeDestroy() {
    if (this.dialogAutoPlayTimer) {
      clearTimeout(this.dialogAutoPlayTimer);
    }
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.AmountTop5-dataAcquisition {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .AmountTop5-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .ChainDistribution-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      .ChainDistribution-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      .ChainDistribution-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .AmountTop5-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 1px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    display: flex;
    flex-direction: column;
    align-items: center;

    /* 顶部统计信息样式 */
    .AmountTop5-top-summary {
      width: 940px;
      text-align: left;
      font-size: 26px;
      color: #fff;
      margin-left: 100px;

      /* 金额高亮样式 */
      .AmountTop5-highlight {
        background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 笔数高亮样式 */
      .AmountTop5-highlight2 {
        margin-left: 20px;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    /* 图表容器样式 */
    .AmountTop5-echart-bar {
      width: 100%;
      height: 19vh;
      margin-top: 0;
    }
  }
}
</style>
