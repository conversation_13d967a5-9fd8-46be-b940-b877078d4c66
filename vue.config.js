const { defineConfig } = require('@vue/cli-service')
const CompressionPlugin = require('compression-webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const isProduction = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: "./",
  productionSourceMap: false, // 生产环境不生成source map
  devServer: {
    proxy: {
      '/api': { // 拦截以 /api 开头的请求
          target: 'https://ryd-uat.cdrongyidai.cn',   // 转发目标地址 
    /*       target: 'https://www.cdrongyidai.cn/', */   // 转发目标地址
        ws: true,
        changeOrigin: true, // 需要虚拟主机站点
        pathRewrite: {
          '^/api': '/api' // 将请求路径中的 /api 重写为空字符串
        }
      },  
      //废弃
/*       "^/manager-api": {
        target: "https://manager-ced-demo.szjrfw.com:10191",
        changeOrigin: true,
        pathRewrite: {
          "^/manager-api": "/api"
        }
      } */
    }
  },
  configureWebpack: config => {
    if (isProduction) {
      // 生产环境优化
      config.plugins.push(
        // gzip压缩
        new CompressionPlugin({
          test: /\.(js|css|html)$/,
          threshold: 10240, // 超过10kb的文件才压缩
          deleteOriginalAssets: false // 保留原文件
        })
      )
      
      // 代码压缩和Tree Shaking配置
      config.optimization = {
        usedExports: true, // 启用Tree Shaking
        minimize: true,
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: false, // 移除console
                drop_debugger: true, // 移除debugger
                pure_funcs: ['console.log'] // 移除console.log
              },
              mangle: true, // 混淆变量名
              output: {
                comments: false // 移除注释
              }
            },
            extractComments: false // 不将注释提取到单独的文件中
          }),
          new CssMinimizerPlugin({
            minimizerOptions: {
              preset: [
                'default',
                {
                  discardComments: { removeAll: true },
                  normalizeWhitespace: true,
                  minifyFontValues: true,
                  minifyGradients: true,
                  minifyParams: true,
                  minifySelectors: true,
                  mergeLonghand: true,
                  mergeRules: true,
                  normalizeCharset: true,
                  normalizeDisplayValues: true,
                  normalizePositions: true,
                  normalizeRepeatStyle: true,
                  normalizeString: true,
                  normalizeTimingFunctions: true,
                  normalizeUnicode: true,
                  normalizeUrl: true,
                  orderedValues: true,
                  reduceInitial: true,
                  reduceTransforms: true,
                  uniqueSelectors: true
                }
              ]
            }
          })
        ],
        // 分割代码块
        splitChunks: {
          chunks: 'all', // 对所有chunks进行分割
          minSize: 20000, // 生成 chunk 的最小体积（以 bytes 为单位）
          maxSize: 244000, // 生成 chunk 的最大体积（以 bytes 为单位）
          minChunks: 1, // 拆分前必须共享模块的最小 chunks 数
          maxAsyncRequests: 30, // 按需加载时的最大并行请求数
          maxInitialRequests: 30, // 入口点的最大并行请求数
          automaticNameDelimiter: '~', // 名称分隔符
          enforceSizeThreshold: 50000, // 强制执行拆分的体积阈值
          cacheGroups: {
            // 第三方库
            vendors: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: -10,
              chunks: 'initial'
            },
            // UI组件库
            elementUI: {
              name: 'chunk-elementUI',
              priority: 20,
              test: /[\\/]node_modules[\\/]element-ui[\\/]/
            },
            // echarts单独打包
            echarts: {
              name: 'chunk-echarts',
              priority: 30,
              test: /[\\/]node_modules[\\/](echarts|echarts-gl)[\\/]/
            },
            // 公共模块
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: 5,
              reuseExistingChunk: true,
              chunks: 'initial'
            },
            // 样式文件
            styles: {
              name: 'styles',
              test: /\.(sa|sc|c)ss$/,
              chunks: 'all',
              enforce: true,
              priority: 10
            },
            // 工具库
            utils: {
              name: 'chunk-utils',
              test: /[\\/]src[\\/]utils[\\/]/,
              minChunks: 1,
              priority: 20,
              reuseExistingChunk: true
            },
            // 组件
            components: {
              name: 'chunk-components',
              test: /[\\/]src[\\/]components[\\/]/,
              minChunks: 1,
              priority: 20,
              reuseExistingChunk: true
            },
            // 路由
            routes: {
              name: 'chunk-routes',
              test: /[\\/]src[\\/]router[\\/]/,
              minChunks: 1,
              priority: 20,
              reuseExistingChunk: true
            },
            // Vuex
            vuex: {
              name: 'chunk-vuex',
              test: /[\\/]src[\\/]store[\\/]/,
              minChunks: 1,
              priority: 20,
              reuseExistingChunk: true
            }
          }
        }
      }
    }
    if (process.env.npm_config_report) {
      config.plugins.push(new BundleAnalyzerPlugin());
    }
  },
  css: {
    // 全局配置 utils.scs，详细配置参考 vue-cli 官网
    extract: isProduction, // 生产环境下将CSS提取到单独的文件
    sourceMap: false, // 不需要CSS的sourceMap
    loaderOptions: {
      sass: {
        additionalData: `@use "@/styles/utils.scss" as *;`
      },
    },
  }
})
